# Brand Insights

A Django-based analytics application for brand sales data visualization and insights, using ClickHouse for high-performance data processing.

## Overview

Brand Insights is a web application that provides analytics and insights for brand sales data. It uses Django with Django REST Framework for the API backend and ClickHouse as the database for high-performance analytics queries. The application allows filtering and aggregation of sales data by various dimensions such as brand, city, product, and date.

## Features

- REST API for accessing sales data with filtering capabilities
- Sales data aggregation by city, brand, and product
- High-performance analytics using ClickHouse database
- Docker containerization for easy deployment

## Prerequisites

- Docker and Docker Compose
- Python 3.12 (if running locally)
- PostgreSQL (for Django's default database)
- ClickHouse (for analytics data)

## Getting Started

### Using Docker Compose (Recommended)

1. Clone the repository:
   ```
   git clone <repository-url>
   cd BRAND-INSIGHTS
   ```

2. Create a .env file in the root directory (you can copy from .env.example if it exists)

3. Start the services using docker-compose:
   ```
   docker-compose up -d
   ```

4. The application will be available at http://localhost:8000
   
5. To stop the services:
   ```
   docker-compose down
   ```

### Local Development Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd BRAND-INSIGHTS
   ```

2. Install dependencies:
   ```
   cd app
   pip install -r requirements.txt
   ```

3. Set up the database:
   ```
   cd brands
   python manage.py makemigrations
   python manage.py migrate
   ```

4. Create an admin user:
   ```
   python manage.py createadmin
   ```

5. Run the development server:
   ```
   python manage.py runserver
   ```

6. The application will be available at http://localhost:8000

## API Endpoints

### City Sales Data

```
GET /api/sales/city/
```

Parameters:
- `brand`: Filter by brand name
- `city`: Filter by city name
- `product`: Filter by product name

Example:
```
GET /api/sales/city/?brand=Nike&city=New%20York
```

Response:
```json
[
  {
    "city": "New York",
    "sales": 12500.00,
    "customers": 350,
    "store": "Manhattan Store"
  }
]
```

## Project Structure

```
app/
├── brands/                # Main Django project directory
│   ├── authz/            # Authentication and authorization
│   ├── crux/             # Core functionality and models
│   ├── insights/         # Analytics and data insights
│   │   ├── api/          # API endpoints
│   │   ├── models/       # Data models
│   │   └── views/        # View controllers
│   └── manage.py         # Django management script
├── requirements.txt      # Python dependencies
└── Dockerfile            # Docker configuration
```

## Technology Stack

- **Backend**: Django 5.1, Django REST Framework 3.14.0
- **Database**: 
  - PostgreSQL (Django ORM)
  - ClickHouse (Analytics)
- **Containerization**: Docker

## License

[Include license information here]

## Contact

[Include contact information here]
