class DatabaseRouter:
    """
    A router to control database operations for different apps.
    
    This router directs:
    - insights models to the ClickHouse database
    - All other models to the default PostgreSQL database
    """
    
    def db_for_read(self, model, **hints):
        """
        Suggest the database that should be used for read operations.
        """
        if model._meta.app_label == 'insights':
            return 'clickhouse'
        return 'default'
    
    def db_for_write(self, model, **hints):
        """
        Suggest the database that should be used for write operations.
        """
        if model._meta.app_label == 'insights':
            return 'clickhouse'
        return 'default'
    
    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if both objects are in the same database.
        """
        # Allow relations between models in the same database
        db1 = self.db_for_read(obj1.__class__)
        db2 = self.db_for_read(obj2.__class__)
        if db1 == db2:
            return True
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the insights.models.sales models only appear in the ClickHouse database.
        """
        if db == 'clickhouse':
            # Only allow migrations for the insights app's sales models in the ClickHouse database
            if app_label == 'insights':
                return True
            return False
        
        # For the default database, don't allow migrations for insights sales models
        if app_label == 'insights':
            return False
        
        # Allow all other migrations in the default database
        return True
