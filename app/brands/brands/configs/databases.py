import os

DB_CONFIG = {   
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'brands'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'postgres'),
        'HOST': os.getenv('DB_HOST', 'db'),
        'PORT': os.getenv('DB_PORT', '5432'),
    },
    'clickhouse': {
        'ENGINE': 'clickhouse_backend.backend',
        'NAME': os.getenv('CLICKHOUSE_DB', 'brands'),
        'HOST': os.getenv('CLICKHOUSE_HOST', 'clickhouse'),
        'PORT': os.getenv('CLICKHOUSE_PORT', '9000'),
        'USER': os.getenv('CLICKHOUSE_USER', 'default'),
        'PASSWORD': os.getenv('CLICKHOUSE_PASSWORD', ''),
        'TEST': {
            'NAME': 'test_' + os.getenv('CLICKHOUSE_DB', 'brands'),
        },
        'OPTIONS': {
            'settings': {'mutations_sync': 1},
        }
    }
}
