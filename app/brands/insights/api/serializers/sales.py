from rest_framework import serializers
from insights.models.sales import ClickHouseSale

class CitySalesWithFilterSerializer(serializers.ModelSerializer):
    brand_filter = serializers.CharField(write_only=True, required=False)
    city_filter = serializers.CharField(write_only=True, required=False)
    product_filter = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = ClickHouseSale
        fields = ('city', 'sales', 'customers', 'brand_filter', 'city_filter', 'product_filter')
        
    def to_representation(self, instance):
        # Apply filters before grouping
        queryset = instance
        brand = self.context.get('brand_filter')
        if brand:
            queryset = queryset.filter(brand=brand)

        city = self.context.get('city_filter')
        if city:
            queryset = queryset.filter(city=city)

        product = self.context.get('product_filter')
        if product:
            queryset = queryset.filter(product=product)

        queryset = queryset.values('city').annotate(
            sales=models.Sum('sales'),
            customers=models.Sum('customers'),
        )

        # Then proceed with grouping
        return super().to_representation(queryset)
