from rest_framework.views import APIView
from rest_framework.response import Response
from insights.models.sales import ClickHouseSale
from .serializers.sales import CitySalesWithFilterSerializer
from django.http import JsonResponse
from django.db.models import Sum


class CitySalesView(APIView):
    """
    API view for city sales data with filtering options.
    
    GET parameters:
    - brand: Filter by brand name
    - city: Filter by city name
    - product: Filter by product name
    """
    def get(self, request):
        queryset = ClickHouseSale.objects.all()
        
        # Get filter parameters from request
        brand = request.query_params.get('brand')
        city = request.query_params.get('city')
        product = request.query_params.get('product')
        
        # Create context with filters
        context = {}
        if brand:
            context['brand_filter'] = brand
        if city:
            context['city_filter'] = city
        if product:
            context['product_filter'] = product


        queryset = queryset.values('city').annotate(
            sales=Sum('sales'),
            customers=Sum('customers')
        )

        data = list(queryset.values('city', 'sales', 'customers', 'store'))
        return JsonResponse(data, safe=False)
       