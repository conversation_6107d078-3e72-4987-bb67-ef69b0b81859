from rest_framework.views import APIView
from insights.models.sales import ClickHouseSale
from django.http import JsonResponse
from django.db.models import Sum


class StoreSalesView(APIView):
    """
    API view for store sales data with filtering options.
    
    GET parameters:
    - brand: Filter by brand name
    - subcategory: Filter by subcategory name
    - store: Filter by store name
    - city: Filter by city name
    """
    def get(self, request):
        queryset = ClickHouseSale.objects.all()
        
        # Get filter parameters from request
        brand = request.query_params.get('brand')
        subcategory = request.query_params.get('subcategory')
        store = request.query_params.get('store')
        city = request.query_params.get('city')

        # Create context with filters
        context = {}

        # Helper function to apply filters with comma-separated values
        def apply_filter(queryset, field_name, filter_value):
            if not filter_value:
                return queryset

            if ',' in filter_value:
                values = [val.strip() for val in filter_value.split(',')]
                return queryset.filter(**{f"{field_name}__in": values})
            else:
                return queryset.filter(**{field_name: filter_value})

        # Apply filters except for store (to get global max sales)
        unfiltered_queryset = queryset
        if brand:
            context['brand_filter'] = brand
            unfiltered_queryset = apply_filter(unfiltered_queryset, 'brand', brand)
            queryset = apply_filter(queryset, 'brand', brand)

        if city:
            context['city_filter'] = city
            unfiltered_queryset = apply_filter(unfiltered_queryset, 'city', city)
            queryset = apply_filter(queryset, 'city', city)

        if subcategory:
            context['subcategory_filter'] = subcategory
            unfiltered_queryset = apply_filter(unfiltered_queryset, 'subcategory', subcategory)
            queryset = apply_filter(queryset, 'subcategory', subcategory)

        # Apply store filter only to the response queryset
        if store:
            context['store_filter'] = store
            queryset = apply_filter(queryset, 'store', store)

        # Aggregate data for response
        queryset = queryset.values('store').annotate(
            sales=Sum('sales'),
            customers=Sum('customers')
        )

        # Aggregate data for unfiltered stores to find max sales
        unfiltered_sales = unfiltered_queryset.values('store').annotate(
            sales=Sum('sales')
        )

        data = list(queryset.values('store', 'sales', 'customers'))

        # Find the top store sales from unfiltered data
        top_store_sales = max(
            [store['sales'] for store in unfiltered_sales] or [0]
        )  # Use 0 if no stores to avoid errors

        # Calculate percentage and performance
        for store in data:
            store['percentage'] = (
                (store['sales'] / top_store_sales) * 100 if top_store_sales > 0 else 0
            )

            # Add performance category
            if 0 <= store['percentage'] < 30:
                store['performance'] = 'poor'
            elif 30 <= store['percentage'] < 60:
                store['performance'] = 'average'
            elif 60 <= store['percentage'] <= 100:
                store['performance'] = 'good'
            else:
                store['performance'] = 'poor'

        data = sorted(data, key=lambda x: x['percentage'], reverse=True)
        return JsonResponse(data, safe=False)