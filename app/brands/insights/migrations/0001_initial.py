# Generated by Django 5.1.8 on 2025-06-02 17:04

import clickhouse_backend.models
import django.core.validators
import django.db.models.manager
import uuid
from django.db import migrations


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ClickHouseSale',
            fields=[
                ('id', clickhouse_backend.models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('brand', clickhouse_backend.models.StringField(max_length=100)),
                ('store', clickhouse_backend.models.StringField(max_length=50)),
                ('product', clickhouse_backend.models.StringField(max_length=100)),
                ('sales', clickhouse_backend.models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('customers', clickhouse_backend.models.Int32Field(validators=[django.core.validators.MinValueValidator(1)])),
                ('pincode', clickhouse_backend.models.StringField(max_length=10)),
                ('city', clickhouse_backend.models.StringField(max_length=50)),
                ('subcategory', clickhouse_backend.models.StringField(max_length=50)),
            ],
            options={
                'db_table': 'CLICKHOUSE_SALES',
                'engine': clickhouse_backend.models.MergeTree(primary_key='id'),
            },
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('_overwrite_base_manager', django.db.models.manager.Manager()),
            ],
        ),
    ]
