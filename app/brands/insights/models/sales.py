from uuid import uuid4
from django.core.validators import MinValueValidator
from clickhouse_backend import models

class ClickHouseSale(models.ClickhouseModel):
    """
    Sales data model optimized for ClickHouse analytics.
    Stores transactional data for high-performance querying and aggregation.
    """
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    brand = models.StringField(max_length=100, db_index=True)
    store = models.StringField(max_length=50, db_index=True)
    product = models.StringField(max_length=100, db_index=True)
    sales = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    customers = models.Int32Field(validators=[MinValueValidator(1)])
    pincode = models.StringField(max_length=10)
    city = models.StringField(max_length=50, db_index=True)
    subcategory = models.StringField(max_length=50, db_index=True)

    class Meta:
        db_table = "CLICKHOUSE_SALES"
        engine = models.MergeTree(
            primary_key='id',
        )

    def __str__(self):
        return f"{self.brand} - {self.product} - {self.date}"

    def clean(self):
        """Validate model fields before saving."""
        super().clean()
        if not self.pincode.isalnum():
            raise ValidationError({"pincode": "Pincode must be alphanumeric."})

    @classmethod
    def get_sales_by_brand(cls, brand: str, start_date=None, end_date=None):
        """
        Retrieve total sales for a brand within a date range.
        
        Args:
            brand (str): Brand name to filter by.
            start_date (date, optional): Start date for filtering.
            end_date (date, optional): End date for filtering.
        
        Returns:
            Decimal: Total sales amount.
        """
        queryset = cls.objects.filter(brand=brand)
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        return queryset.aggregate(total_sales=models.Sum('sales'))['total_sales'] or 0