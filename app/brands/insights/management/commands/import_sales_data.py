import csv
import uuid
from pathlib import Path
from django.core.management.base import BaseCommand
from insights.models.sales import ClickHouseSale
from crux.models.masters import StoreMaster, CityMaster

class Command(BaseCommand):
    help = "Import sales data from CSV file into ClickHouse"

    def handle(self, *args, **kwargs):
        # Path to the CSV file
        csv_file_path = Path('/application/data/brands_level_data_may_jun.csv')
        
        # For local development
        if not csv_file_path.exists():
            csv_file_path = Path('app/brands/data/brands_level_data_may_jun.csv')
            
        if not csv_file_path.exists():
            self.stdout.write(self.style.ERROR(f"Sales data CSV file not found at {csv_file_path}"))
            return
            
        # Track statistics
        records_created = 0
        records_skipped = 0
        
        # Store city and pincode mapping for faster lookups
        city_pincode_map = {}
        
        # Read CSV file
        self.stdout.write(self.style.NOTICE(f"Reading sales data from {csv_file_path}"))
        
        with open(csv_file_path, 'r') as csvfile:
            csv_reader = csv.reader(csvfile)
            # Skip header row
            header = next(csv_reader)
            
            # Process each row
            for row in csv_reader:
                if len(row) < 8:
                    records_skipped += 1
                    continue
                    
                store = row[0].strip()
                city_name = row[1].strip()
                brand = row[2].strip()
                category = row[3].strip()
                product = row[4].strip()
                pincode = row[5].strip()
                sales = float(row[6].strip() or 0)
                customers = int(float(row[7].strip() or 0))
                
                # Use subcategory from category if available
                subcategory = category
                
                # Get pincode from map or database
                if city_name not in city_pincode_map:
                    # Try to find the city in the database
                    try:
                        city = CityMaster.objects.filter(name=city_name).first()
                        if city:
                            city_pincode_map[city_name] = city.pincode
                        else:
                            # If city not found, use the pincode from CSV
                            city_pincode_map[city_name] = pincode
                    except Exception as e:
                        self.stdout.write(self.style.WARNING(f"Error finding city {city_name}: {e}"))
                        city_pincode_map[city_name] = pincode
                
                # Create the sales record
                try:
                    sale = ClickHouseSale(
                        id=uuid.uuid4(),
                        brand=brand,
                        store=store,
                        city=city_name,
                        product=product,
                        sales=sales,
                        customers=customers,
                        pincode=city_pincode_map.get(city_name, pincode),
                        subcategory=subcategory
                    )
                    sale.save(using='clickhouse')
                    records_created += 1
                    
                    # Show progress every 1000 records
                    if records_created % 1000 == 0:
                        self.stdout.write(f"Processed {records_created} records...")
                        
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Error creating sales record: {e}"))
                    records_skipped += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully imported {records_created} sales records. '
                f'Skipped {records_skipped} records.'
            )
        )
