import csv
from pathlib import Path
from django.core.management.base import BaseCommand
from crux.models.masters import BrandMaster

class Command(BaseCommand):
    help = "Create brand records in the BrandMaster table from CSV data"

    def handle(self, *args, **kwargs):
        # Path to the CSV file inside Docker container
        csv_file_path = Path('/application/data/brands_data.csv')

        if not csv_file_path.exists():
            self.stdout.write(self.style.ERROR(f"CSV file not found at {csv_file_path}"))
            return

        # Track statistics
        brands_created = 0
        brands_updated = 0
        
        # Dictionary to track brand data
        brand_data = {}

        # Read CSV file
        self.stdout.write(self.style.NOTICE(f"Reading data from {csv_file_path}"))
        
        with open(csv_file_path, 'r') as csvfile:
            csv_reader = csv.reader(csvfile)
            # Skip header row
            next(csv_reader)
            
            # Process each row
            for row in csv_reader:
                if len(row) < 3:
                    continue
                    
                brand_name = row[0].strip()
                category = row[1].strip()
                count = int(row[2].strip())
                
                # Collect brand data
                if brand_name not in brand_data:
                    brand_data[brand_name] = {
                        'category': category,
                        'count': count
                    }
                else:
                    # If brand appears in multiple categories, use the one with highest count
                    if count > brand_data[brand_name]['count']:
                        brand_data[brand_name]['category'] = category
                        brand_data[brand_name]['count'] = count
        
        # Create or update brands
        for brand_name, data in brand_data.items():
            category = data['category']
            count = data['count']
            
            # Generate a description based on category and popularity
            description = f"A popular {category} brand with {count} products in our database."
            
            # Create or update brand
            brand, created = BrandMaster.objects.update_or_create(
                name=brand_name,
                defaults={
                    'description': description,
                    'category': category
                }
            )

            if created:
                brands_created += 1
            else:
                brands_updated += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {brands_created} new brands and updated {brands_updated} existing brands '
                f'from the CSV data.'
            )
        )
