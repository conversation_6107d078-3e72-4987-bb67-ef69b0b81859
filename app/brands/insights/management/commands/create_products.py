import random
from django.core.management.base import BaseCommand
from crux.models.masters import BrandMaster, ProductMaster

# Product data based on the PRODUCTS list from create_sales_data.py
PRODUCTS = [
    ("Parle-G Biscuits", "Parle", "Biscuits", "Popular glucose biscuits", 10.00, 1000),
    ("Maggi Noodles", "Maggi", "Noodles", "Instant noodles with masala flavor", 14.00, 800),
    ("Fogg", "Fogg", "Personal Care", "Long-lasting deodorant", 199.00, 500),
    ("Amul Butter", "Amul", "Dairy", "Creamy table butter", 50.00, 600),
    ("Britannia Bourbon Biscuits", "Britannia", "Biscuits", "Chocolate cream biscuits", 30.00, 750),
    ("Tata Salt", "Tata", "Seasoning", "Iodized table salt", 20.00, 1200),
    ("Surf Excel Detergent", "Surf Excel", "Detergent", "Stain removal detergent powder", 120.00, 400),
    ("Colgate Toothpaste", "Colgate", "Personal Care", "Cavity protection toothpaste", 80.00, 600),
    ("Dove Soap", "Dove", "Personal Care", "Moisturizing beauty bar", 45.00, 800),
    ("Sunfeast Yippee Noodles", "Sunfeast", "Noodles", "Instant noodles with magic masala", 15.00, 700),
    ("Patanjali Ghee", "Patanjali", "Dairy", "Pure cow ghee", 550.00, 300),
    ("Aashirvaad Atta", "Aashirvaad", "Flour", "Whole wheat flour", 250.00, 450),
    ("Nescafe Coffee", "Nescafe", "Beverages", "Instant coffee powder", 180.00, 350),
    ("Lux Body Wash", "Lux", "Personal Care", "Fragrant body wash", 150.00, 400),
    ("Dettol Antiseptic Liquid", "Dettol", "Personal Care", "Germ protection liquid", 125.00, 550),
    ("Good Day Biscuits", "Good Day", "Biscuits", "Butter cookies", 25.00, 900),
    ("Horlicks Health Drink", "Horlicks", "Health Drink", "Malted milk powder", 280.00, 250),
    ("Ponds Face Cream", "Ponds", "Skincare", "Moisturizing face cream", 110.00, 300),
    ("Dabur Honey", "Dabur", "Honey", "Pure honey", 220.00, 400),
    ("Lays Chips", "Lays", "Snacks", "Potato chips with various flavors", 20.00, 1000),
]

class Command(BaseCommand):
    help = "Create product records in the ProductMaster table"

    def handle(self, *args, **kwargs):
        # Clear existing products if needed
        # ProductMaster.objects.all().delete()
        
        # Create product records
        products_created = 0
        products_skipped = 0
        brands_not_found = 0
        
        for name, brand_name, category, description, price, stock in PRODUCTS:
            # Check if product already exists to avoid duplicates
            if ProductMaster.objects.filter(name=name).exists():
                products_skipped += 1
                continue
                
            # Get the brand instance
            try:
                brand = BrandMaster.objects.get(name=brand_name)
            except BrandMaster.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f"Brand '{brand_name}' not found. Skipping product '{name}'")
                )
                brands_not_found += 1
                continue
            
            # Create the product
            ProductMaster.objects.create(
                brand=brand,
                name=name,
                description=description,
                price=price,
                stock=stock,
                category=category
            )
            products_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {products_created} products. '
                f'Skipped {products_skipped} existing products. '
                f'Could not create {brands_not_found} products due to missing brands.'
            )
        )
