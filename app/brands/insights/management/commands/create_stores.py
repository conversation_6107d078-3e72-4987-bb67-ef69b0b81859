from django.core.management.base import BaseCommand
from crux.models.masters import CityMaster, StoreMaster
import random

# Real store data from the provided list
STORE_DATA = [
    {'Store': 'hr007_rjv_ls1', 'City': 'Faridabad', 'State': 'Haryana', 'Pin Code': '121004', 'Address': 'Rajeev Colony Market, Near Kanchan School, Samaypur Road, PIN - 121004, Haryana, India.'},
    {'Store': 'up068_nin_ls1', 'City': 'Barabanki', 'State': 'Uttar pradesh', 'Pin Code': '225302', 'Address': 'Nindura, Near Odaria Tiraha, Babaganj Road, P.O. - Nindura, P. S. - Ghumghter, Dist. - Barabanki, PIN - 225302, Uttar Pradesh, India.'},
    {'Store': 'up064_bha_ls1', 'City': 'Unnao', 'State': 'Uttar pradesh', 'Pin Code': '229502', 'Address': '14, Bhagwat Nagar, Unnao, Purwa , Dist. - Unnao, PIN - 229502, Uttar Pradesh, India.'},
    {'Store': 'up059_diw_ls1', 'City': 'Diwanganj', 'State': 'Uttar pradesh', 'Pin Code': '230142', 'Address': 'Diwanganj Bazar, Diwanganj, P.O. - Shitlaganj, P.S. - Diwanganj, Dist. - Pratapgarh, PIN - 230142, Uttar Pradesh, India'},
    {'Store': 'up072_moh_ls1', 'City': 'Amethi', 'State': 'Uttar Pradesh', 'Pin Code': '229309', 'Address': 'Mohanganj Bazar, P.O. - Tiloi, P.S. - Mohanganj, Dist. - Amethi, PIN - 229309, Uttar Pradesh, India.'},
    {'Store': 'up061_kur_ls1', 'City': 'Sultanpur', 'State': 'Uttar Pradesh', 'Pin Code': '228155', 'Address': 'Kurwar Bazar, Near Canara Bank, P.O. - Kurwar, P.S. - Kurwar, Dist. - Sultanpur, PIN - 228155, Uttar Pradesh, India.'},
    {'Store': 'up051_has_ls1', 'City': 'Unnao', 'State': 'Uttar Pradesh', 'Pin Code': '209841', 'Address': 'GATA Number 271, Munshiganj Road, Mehdi Khera Chowraha, Hasan Ganj, Unnao, Uttar Pradesh - 209841, India.'},
    {'Store': 'UP047_AMT_LMDC2', 'City': 'Amethi', 'State': 'Uttar Pradesh', 'Pin Code': '227405', 'Address': 'Kakwa Road, Mangapur, P.O. - Bariyapur, P.S. - Amethi, Dist - Amethi, PIN  - 227405, Uttar Pradesh, India.'},
    {'Store': 'UP046_RBL_TEMP1', 'City': 'Raebareli', 'State': 'Uttar Pradesh', 'Pin Code': '229010', 'Address': 'Doorbhash Nagar, Raebareli,P.O. - Raebareli, P.S. - Raebareli, Dist. - Raebareli, PIN - 229010, Uttar Pradesh, India.'},
    {'Store': 'UP045_LKO_TEMP1', 'City': 'Lucknow', 'State': 'Uttar Pradesh', 'Pin Code': '226008', 'Address': 'KN-42 /WH-01 miranpur pinvat daroga khera kanpur road Lucknow - 226008'},
    {'Store': 'up044_JAS_LS1', 'City': 'Raebareli', 'State': 'Uttar Pradesh', 'Pin Code': '229305', 'Address': 'Agarwal Complex, Nasirabad Road, Jais, P.O. - Jais, P.S. - Jais, Dist. - Amethi, PIN - 229305, Uttar Pradesh, India.'},
    {'Store': 'up043_GAU_LS1', 'City': 'Raebareli', 'State': 'Uttar Pradesh', 'Pin Code': '229204', 'Address': 'Deenshah ,near state Bank,gaura, Raebareli, 229204, UP'},
    {'Store': 'UP041_KAD_LMDC1', 'City': 'Uttar Pradesh', 'State': 'Uttar Pradesh', 'Pin Code': '228145', 'Address': 'Pandey Baba, Uttar Pradesh - 228145, Uttar Pradesh, India.'},
    {'Store': 'UP042_SUL_LMDC1', 'City': 'Lucknow', 'State': 'Uttar Pradesh', 'Pin Code': '228001', 'Address': 'Jogipur Chauraha, Ahimane Bazar, Sultanpur - Pratapgarh Road, Lakhipur, Sultanpur, Uttar Pradesh-228001'},
    {'Store': 'UP040_AMT_LMDC1', 'City': 'Lucknow', 'State': 'Uttar Pradesh', 'Pin Code': '227405', 'Address': 'Kakwa Road, Mangapur, P.O. - Bariyapur, P.S. - Amethi, Dist - Amethi, PIN  - 227405, Uttar Pradesh, India.'},
    {'Store': 'UP039_SAF_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '209871', 'Address': 'Sitapur - Kanpur Road, Safipur Rural, Safipur, Uttar Pradesh 209871'},
    {'Store': 'UP038_PUR_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '209825', 'Address': 'PATWA MARKET & COMPLEX, NEAR HDFC BANK, PURWA TOWN, PURWA UNNAO PIN - 209825, UTTAR PRADESH'},
    {'Store': 'UP037_NAW_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '209859', 'Address': 'Maniya Singh Market, P.S. - Nawabganj,P.O. - Nawabganj, Tehsil - Hasanganj,Dist. - Unnao, PIN - 209859, Uttar Pradesh, India.'},
    {'Store': 'UP036_SAN_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '241204', 'Address': '177/162, Lucknow Road, Adjacent to Honda Showroom, Sandila, Hardoi - 241204, Uttar Pradesh'},
    {'Store': 'UP035_KHG_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '212655', 'Address': 'Kishanpur Road, Near Tehsil, Khaga, Fatehpur, Pin - 212655, Uttar Pradesh'},
    {'Store': 'UP034_BKT_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '226201', 'Address': 'Rameshwar Plaza, Bakshi Ka Talab, Sitapur Road, Lucknow - 226201, Uttar Pradesh'},
    {'Store': 'UP033_MOH_HM1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '227305', 'Address': 'Gaurav Complex, Raebareli Road, Near to Police Chowaki, Mohanlalaganj Chowaraha, Mohanlalganj - 227305, Uttar Pradesh'},
    {'Store': 'UP032_SAN_LMDC1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '241204', 'Address': 'Basement of 177/162, Lucknow Road, Adjacent to Honda Showroom, Sandila, Hardoi-241204,Uttar Pradesh, India'},
    {'Store': 'UP031_KHA_LMDC1', 'City': 'Lucknow', 'State': 'Uttar pradesh', 'Pin Code': '212655', 'Address': 'Basement of Kishanpur Road, Near Tehsil, Khaga, Fatepur-212655, Uttar Pradesh, India'},
    {'Store': 'UP030_GAU_LMDC1', 'City': 'Amethi', 'State': 'Uttar Pradesh', 'Pin Code': '227409', 'Address': 'Basement and Ground Floor of Hanumat Complex, Chandi Charn ka Purva,  Ward No. 24, Rajgarh, Gauriganj, Amethi, Uttar Pradesh-  (Beside Old RTO Office)'},
    {'Store': 'UP028_SID_LMDC1', 'City': 'Sitapur', 'State': 'Uttar Pradesh', 'Pin Code': '261303', 'Address': 'Lower Ground, Ground Floor and First Floor of 177/162, Vivek Nagar, G.T. Road, Sidhauli, Sitapur, Uttar Pradesh- India'},
    {'Store': 'UP029_SAL_LMDC1', 'City': 'Raebareli', 'State': 'Uttar Pradesh', 'Pin Code': '229127', 'Address': 'Lower Ground Floor and Upper Ground Floor, Manikpur Road, Salon, Raebareli, Uttar Pradesh (Opposite to Shekhar Rastogi)'},
    {'Store': 'cx1_test_wh', 'City': 'UP', 'State': 'UP', 'Pin Code': '999999', 'Address': 'UP'},
    {'Store': 'UP027_AMT_HM1', 'City': 'Amethi', 'State': 'Uttar Pradesh', 'Pin Code': '227405', 'Address': 'Shukla Compex, Amethi - Musafirkhana Rd, near Hotel International, Amethi, Uttar Pradesh 227405'}
]

# Store types for random assignment
STORE_TYPES = ["Supermarket", "Convenience Store", "Hypermarket", "Department Store", "Discount Store"]

class Command(BaseCommand):
    help = "Create store records in the StoreMaster table using real store data"

    def handle(self, *args, **kwargs):
        # Clear existing stores if needed
        # StoreMaster.objects.all().delete()
        
        # Create store records
        stores_created = 0
        stores_skipped = 0
        cities_created = 0
        cities_dict = {}
        
        # First, ensure all cities exist in the database
        for store_data in STORE_DATA:
            city_name = store_data['City']
            state = store_data['State']
            pincode = store_data['Pin Code']
            
            # Skip if city is already processed
            if city_name in cities_dict:
                continue
                
            # Check if city exists, create if not
            city, created = CityMaster.objects.get_or_create(
                name=city_name,
                defaults={
                    'pincode': pincode,
                    'state': state,
                    'country': 'India'
                }
            )
            
            cities_dict[city_name] = city
            if created:
                cities_created += 1
                
        self.stdout.write(
            self.style.SUCCESS(f'Found/Created {len(cities_dict)} cities (newly created: {cities_created})')
        )
        
        # Now create stores using the real data
        for store_data in STORE_DATA:
            store_name = store_data['Store']
            city_name = store_data['City']
            address = store_data['Address']
            pincode = store_data['Pin Code']
            
            # Skip if store already exists
            if StoreMaster.objects.filter(name=store_name).exists():
                stores_skipped += 1
                continue
                
            # Get city from our dictionary
            city = cities_dict.get(city_name)
            if not city:
                self.stdout.write(
                    self.style.WARNING(f"City '{city_name}' not found for store '{store_name}'. Skipping.")
                )
                continue
                
            # Extract block and street from address if possible
            address_parts = address.split(',')
            
            # Handle block - truncate if needed
            block = address_parts[0] if len(address_parts) > 0 else 'Block A'
            if len(block) > 50:
                block = block[:47] + '...'
                
            # Handle street - truncate if needed
            street = address_parts[1].strip() if len(address_parts) > 1 else 'Main Street'
            if len(street) > 50:
                street = street[:47] + '...'
            
            # Assign a random store type and ensure it's not too long
            store_type = random.choice(STORE_TYPES)
            if len(store_type) > 50:
                store_type = store_type[:47] + '...'
            
            # Create description
            description = f"A {store_type.lower()} located in {city_name}"
            
            # Create the store
            StoreMaster.objects.create(
                city=city,
                name=store_name,
                description=description,
                store_type=store_type,
                pincode=pincode,
                block=block,
                street=street,
                address=address
            )
            stores_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {stores_created} stores. '
                f'Skipped {stores_skipped} existing stores.'
            )
        )
