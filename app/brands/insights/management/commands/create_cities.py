from django.core.management.base import BaseCommand
from crux.models.masters import CityMaster

# Include cities from real store data
REAL_STORE_CITIES = {
    "Faridabad": {"pincode": "121004", "state": "Haryana"},
    "Barabanki": {"pincode": "225302", "state": "Uttar Pradesh"},
    "Unnao": {"pincode": "229502", "state": "Uttar Pradesh"},
    "Diwanganj": {"pincode": "230142", "state": "Uttar Pradesh"},
    "Amethi": {"pincode": "229309", "state": "Uttar Pradesh"},
    "Sultanpur": {"pincode": "228155", "state": "Uttar Pradesh"},
    "Raebareli": {"pincode": "229010", "state": "Uttar Pradesh"},
    "Lucknow": {"pincode": "226008", "state": "Uttar Pradesh"},
    "Uttar Pradesh": {"pincode": "228145", "state": "Uttar Pradesh"},
}


class Command(BaseCommand):
    help = "Create cities in CityMaster model"

    def handle(self, *args, **options):

        # Merge with real store cities, with real store data taking precedence for duplicates
        merged_cities = REAL_STORE_CITIES
        
        cities_created = 0
        cities_updated = 0
        
        for city, info in merged_cities.items():
            city_obj, created = CityMaster.objects.get_or_create(
                name=city,
                defaults={
                    "pincode": info["pincode"],
                    "country": "India",
                    "state": info["state"]
                }
            )
            
            # If the city exists but info is different, update it
            if not created and (city_obj.pincode != info["pincode"] or city_obj.state != info["state"]):
                city_obj.pincode = info["pincode"]
                city_obj.state = info["state"]
                city_obj.save()
                cities_updated += 1
            
            if created:
                cities_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully processed {len(merged_cities)} cities. "
                f"Created: {cities_created}, Updated: {cities_updated}"
            )
        )
