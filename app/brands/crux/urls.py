from django.urls import path
from crux.views.stores import StoreAPIView
from crux.views.city import CityAPIView
from crux.views.brands import BrandAPIView

urlpatterns = [
    path('stores/', StoreAPIView.as_view(), name='store'),
    path('stores/<int:store_id>/', StoreAPIView.as_view(), name='store'),
    path('cities/', CityAPIView.as_view(), name='city'),
    path('cities/<int:city_id>/', CityAPIView.as_view(), name='city'),
    path('brands/', BrandAPIView.as_view(), name='brand'),
    path('brands/<int:brand_id>/', BrandAPIView.as_view(), name='brand'),
]