# Generated by Django 5.1 on 2025-06-01 01:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BaseModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='BrandMaster',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='crux.basemodel')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('category', models.CharField(max_length=50)),
            ],
            options={
                'db_table': 'BRAND_MASTER',
            },
            bases=('crux.basemodel',),
        ),
        migrations.CreateModel(
            name='CityMaster',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='crux.basemodel')),
                ('name', models.CharField(max_length=100)),
                ('pincode', models.CharField(max_length=10)),
                ('country', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=50)),
            ],
            options={
                'db_table': 'CITY_MASTER',
            },
            bases=('crux.basemodel',),
        ),
        migrations.CreateModel(
            name='ProductMaster',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='crux.basemodel')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.IntegerField()),
                ('category', models.CharField(max_length=50)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crux.brandmaster')),
            ],
            options={
                'db_table': 'PRODUCT_MASTER',
            },
            bases=('crux.basemodel',),
        ),
        migrations.CreateModel(
            name='StoreMaster',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='crux.basemodel')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('store_type', models.CharField(max_length=50)),
                ('pincode', models.CharField(max_length=10)),
                ('block', models.CharField(max_length=50)),
                ('street', models.CharField(max_length=50)),
                ('address', models.TextField()),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crux.citymaster')),
            ],
            options={
                'db_table': 'STORE_MASTER',
            },
            bases=('crux.basemodel',),
        ),
    ]
