from django.db import models
from .base import BaseModel

# Create your models here.




class BrandMaster(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=50)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "BRAND_MASTER"


class ProductMaster(BaseModel):
    brand = models.ForeignKey(BrandMaster, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.IntegerField()
    category = models.CharField(max_length=50)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "PRODUCT_MASTER"



class CityMaster(BaseModel):
    name = models.CharField(max_length=100)
    pincode = models.CharField(max_length=10)
    country = models.CharField(max_length=50)
    state = models.Char<PERSON><PERSON>(max_length=50)
    
    def __str__(self):
        return self.name

    class Meta:
        db_table = "CITY_MASTER"


class StoreMaster(BaseModel):
    city = models.ForeignKey(CityMaster, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    description = models.TextField()
    store_type = models.CharField(max_length=50)
    pincode = models.CharField(max_length=10)
    block = models.CharField(max_length=50)
    street = models.CharField(max_length=50)
    address = models.TextField()

    def __str__(self):
        return self.name

    class Meta:
        db_table = "STORE_MASTER"

