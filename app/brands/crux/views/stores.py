from django.http import JsonResponse
from crux.models.masters import StoreMaster
from rest_framework.views import APIView


class StoreAPIView(APIView):
    """
    API view to get a specific store by ID.
    """
    def _get_dict_data(self, store):
        return {
            "id": store.id,
            "city": store.city.name,
            "city_id": store.city.id,
            "state": store.city.state,
            "name": store.name,
            "description": store.description,
            "store_type": store.store_type,
            "pincode": store.pincode,
            "block": store.block,
            "street": store.street,
            "address": store.address,
        }

    def get(self, request, store_id=None):
        """
        Get a store by ID or get a list of stores with a city filter
        """
        city_filter = request.query_params.get('city')
        stores = StoreMaster.objects.all().select_related('city')

        if store_id:
            try:
                store = stores.get(id=store_id)
                data = self._get_dict_data(store)
            except StoreMaster.DoesNotExist:
                return JsonResponse({'detail': 'Store not found.'}, status=404)
        else:
            if city_filter:
                if ',' in city_filter:
                    cities = [city.strip() for city in city_filter.split(',')]
                    stores = stores.filter(city__name__in=cities)
                else:
                    stores = stores.filter(city__name=city_filter)

            data = {
                "stores": [self._get_dict_data(store) for store in stores],
            }
        return JsonResponse(data)

