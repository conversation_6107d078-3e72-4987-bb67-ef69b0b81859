from django.http import JsonResponse
from crux.models.masters import BrandMaster
from rest_framework.views import APIView


class BrandAPIView(APIView):
    """
    API view to get a specific brand by ID or list all brands.
    """
    def _get_dict_data(self, brand):
        return {
            "id": brand.id,
            "name": brand.name,
            "description": brand.description,
            "category": brand.category,
        }

    def get(self, request, brand_id=None):
        """
        Get a brand by ID or get a list of brands with optional category filter
        """
        category_filter = request.query_params.get('category')
        brands = BrandMaster.objects.all()

        if brand_id:
            try:
                brand = brands.get(id=brand_id)
                data = self._get_dict_data(brand)
            except BrandMaster.DoesNotExist:
                return JsonResponse({'detail': 'Brand not found.'}, status=404)
        else:
            if category_filter:
                if ',' in category_filter:
                    categories = [category.strip() for category in category_filter.split(',')]
                    brands = brands.filter(category__in=categories)
                else:
                    brands = brands.filter(category=category_filter)

            data = {
                "brands": [self._get_dict_data(brand) for brand in brands],
            }
        return JsonResponse(data)
