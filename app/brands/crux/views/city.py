from django.http import JsonResponse
from crux.models.masters import CityMaster
from rest_framework.views import APIView


class CityAPIView(APIView):
    """
    API view to get a specific city by ID or list all cities.
    """
    def _get_dict_data(self, city):
        return {
            "id": city.id,
            "name": city.name,
            "pincode": city.pincode,
            "country": city.country,
            "state": city.state,
        }

    def get(self, request, city_id=None):
        state_filter = request.query_params.get('state')
        country_filter = request.query_params.get('country')

        data = {}
        if city_id:
            try:
                city = CityMaster.objects.get(id=city_id)
                data = self._get_dict_data(city)
            except CityMaster.DoesNotExist:
                return JsonResponse({'detail': 'City not found.'}, status=404)
        else:
            cities = CityMaster.objects.all()
            
            # Apply filters if provided
            if state_filter:
                cities = cities.filter(state=state_filter)
            if country_filter:
                cities = cities.filter(country=country_filter)
                
            data = {
                "cities": [self._get_dict_data(city) for city in cities]
            }
            
        return JsonResponse(data)
