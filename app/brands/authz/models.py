from django.db import models

# Create your models here.

from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    Extends the default User model with a new column to store the user's
    preferred language.
    """
    email = models.EmailField(_('Email address'), unique=True)
    email_verified = models.BooleanField(_('Email verified'), default=False)

    def __str__(self):
        return self.username

class Role(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Role"
        verbose_name_plural = "Roles"
        db_table = "ROLES"