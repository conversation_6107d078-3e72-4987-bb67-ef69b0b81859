from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin

from .models import User, Role

# Register your models here.

class UserAdmin(BaseUserAdmin):
    fieldsets = (
        ('Personal Information', {
            'fields': (
                'username',
                'first_name',
                'last_name',
                'password',
                'email',
                'email_verified'
            )
        }),
        ('Permissions', {
            'fields': (
                'is_active',
                'is_staff',
                'is_superuser',
                'groups',
                'user_permissions'
            )
        }),
        ('Important Dates', {
            'fields': (
                'last_login',
                'date_joined'
            )
        })
    )


admin.site.register(User, UserAdmin)
admin.site.register(Role)

